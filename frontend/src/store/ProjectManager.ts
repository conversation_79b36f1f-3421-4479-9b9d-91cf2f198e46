import { Store } from "./Store";
import { HistoryActionType } from "./HistoryManager";
import { Animation, Caption, EditorElement, Track, TrackType } from "../types";

const CONSTANTS = {
  SAVE: {
    AUTO_SAVE_KEY: "fabric-canvas-state",
  },
};

export class ProjectManager {
  private store: Store;
  projectName: string = "Untitled video";
  private autoSaveKey = CONSTANTS.SAVE.AUTO_SAVE_KEY;
  private _isImporting: boolean = false;

  constructor(store: Store) {
    this.store = store;
  }

  get isImporting(): boolean {
    return this._isImporting;
  }

  setProjectName(name: string) {
    this.projectName = name;
    this.store.saveChange();
  }

  private _processAnimationForTransition(
    elementAnimations: Animation[],
    group: "in" | "out",
    elementId: string
  ): { type: string; duration?: number } {
    const animation = elementAnimations.find((anim) => anim.group === group);
    if (animation) {
      const duration = animation.duration / 1000;
      console.log(
        `🎬 ${group === "in" ? "入场" : "出场"}动画 - 元素 ${elementId}: 类型=${
          animation.type
        }, 时间=${duration}秒`
      );
      return {
        type: this.mapAnimationTypeToXfadeTransition(
          animation.type,
          animation.properties
        ),
        duration: duration,
      };
    }
    return { type: "none" };
  }

  /**
   * 将前端动画类型映射到FFmpeg xfade transition类型
   * @param animationType 前端动画类型
   * @param properties 动画属性
   * @returns FFmpeg xfade transition类型
   */
  private mapAnimationTypeToXfadeTransition(
    animationType: string,
    properties?: any
  ): string {
    switch (animationType) {
      case "fadeIn":
      case "fadeOut":
        return "fade";

      case "slideIn":
      case "slideOut":
        // 检查是否使用clipPath来区分slide和wipe动画
        const useClipPath = properties?.useClipPath;
        const direction = properties?.direction;

        if (useClipPath) {
          // Wipe动画：使用wipe系列效果
          switch (direction) {
            case "left":
              return "wiperight";
            case "right":
              return "wipeleft";
            case "top":
              return "wipedown";
            case "bottom":
              return "wipeup";
            default:
              return "fade"; // 默认使用fade
          }
        } else {
          // Slide动画：使用slide系列效果
          switch (direction) {
            case "left":
              return "slideright";
            case "right":
              return "slideleft";
            case "top":
              return "slidedown";
            case "bottom":
              return "slideup";
            default:
              return "fade"; // 默认使用fade
          }
        }

      case "breathe":
        return "fade"; // 呼吸效果使用fade

      case "rotate":
        return "circleopen"; // 旋转效果使用circleopen

      case "bounce":
      case "shake":
        return "fade"; // 弹跳和抖动效果使用fade

      case "flash":
        return "fade"; // 闪烁效果使用fade

      case "zoom":
      case "zoomIn":
      case "zoomOut":
        return "circleopen"; // 缩放效果使用circleopen

      default:
        return "fade"; // 默认使用fade
    }
  }

  private convertAnimationsToTransitions(elementId: string) {
    const elementAnimations = this.store.animations.filter(
      (animation) => animation.targetId === elementId
    );

    const transition: {
      in?: string;
      out?: string;
      inDuration?: number;
      outDuration?: number;
      duration?: number;
    } = {};

    const inTransition = this._processAnimationForTransition(
      elementAnimations,
      "in",
      elementId
    );
    transition.in = inTransition.type;
    if (inTransition.duration) {
      transition.inDuration = inTransition.duration;
    }

    const outTransition = this._processAnimationForTransition(
      elementAnimations,
      "out",
      elementId
    );
    transition.out = outTransition.type;
    if (outTransition.duration) {
      transition.outDuration = outTransition.duration;
    }

    if (transition.inDuration && !transition.outDuration) {
      transition.duration = transition.inDuration;
    } else if (transition.outDuration && !transition.inDuration) {
      transition.duration = transition.outDuration;
    } else if (transition.inDuration && transition.outDuration) {
      transition.duration = transition.inDuration;
    }

    return transition;
  }

  exportCanvasState() {
    if (!this.store.canvas) {
      return null;
    }

    // Create a clean copy of canvas state without fabric.js objects
    const canvasState = {
      backgroundColor: this.store.backgroundColor,
      width: this.store.canvasWidth,
      height: this.store.canvasHeight,
      elements: this.store.editorElements.map(
        ({ fabricObject, ...element }) => {
          // 为每个元素添加transition信息（仅用于后端视频生成）
          const transition = this.convertAnimationsToTransitions(element.id);

          // 调试日志：检查动画转换
          const elementAnimations = this.store.animations.filter(
            (animation) => animation.targetId === element.id
          );
          if (elementAnimations.length > 0) {
            console.log(`🔍 动画调试 - 元素 ${element.id}:`);
            console.log("原始动画:", elementAnimations);
            console.log("转换后的transition:", transition);
          }

          return {
            ...element,
            // 添加transition属性，专门用于后端FFmpeg处理
            // 注意：这不会替代animations数组，而是作为后端专用的简化数据
            transition,
            placement: element.placement
              ? {
                  ...element.placement,
                  x: element.placement.x
                    ? Number(element.placement.x.toFixed(2))
                    : 0,
                  y: element.placement.y
                    ? Number(element.placement.y.toFixed(2))
                    : 0,
                  width: element.placement.width
                    ? Number(element.placement.width.toFixed(2))
                    : 0,
                  height: element.placement.height
                    ? Number(element.placement.height.toFixed(2))
                    : 0,
                }
              : null,
            timeFrame: {
              start: Number(element.timeFrame.start.toFixed(2)),
              end: Number(element.timeFrame.end.toFixed(2)),
            },
          };
        }
      ),
      animations: this.store.animations,
      captions: this.store.captions,
      // 添加全局字幕样式
      globalCaptionStyle: this.store.captionManager.globalCaptionStyle,
      // 添加轨道信息
      tracks: this.store.trackManager.tracks,
      // 添加默认轨道信息
      // defaultTracks: this.trackManager.defaultTracks,
    };
    console.log("导出的画布状态（包含transition信息）:", canvasState);
    return JSON.stringify(canvasState);
  }

  private _resetStateForImport() {
    this.store.historyManager.clearHistory();
    this.store.setEditorElements([]);
    this.store.animations = [];
    this.store.trackManager.tracks = [];
    this.store.trackManager.defaultTracks = {
      media: "",
      audio: "",
      text: "",
      caption: "",
    };
  }

  private _importCanvasSettings(canvasData: any) {
    this.store.setCanvasSize(canvasData.width, canvasData.height);
    this.store.setBackgroundColor(canvasData.backgroundColor);
  }

  private _importCaptions(canvasData: any) {
    if (canvasData.captions) {
      this.store.captionManager.setCaptions(canvasData.captions);
      this.store.captions = this.store.captionManager.captions;
    }
    if (canvasData.globalCaptionStyle) {
      this.store.captionManager.globalCaptionStyle =
        canvasData.globalCaptionStyle;
    }
  }

  private _importTracks(canvasData: any) {
    if (canvasData.tracks) {
      this.store.trackManager.tracks = canvasData.tracks;
    }
    if (canvasData.defaultTracks) {
      this.store.trackManager.defaultTracks = canvasData.defaultTracks;
    }
  }
  private _importAnimations(canvasData: any) {
    if (canvasData.animations) {
      this.store.animations = canvasData.animations;
    }
  }
  private _createElementAsync(element: any): Promise<void> {
    return new Promise((resolve) => {
      const elementCreators: { [key: string]: () => void } = {
        text: () => {
          this.store.setEditorElements([element, ...this.store.editorElements]);
          this.store.elementManager.addElement(element);
          resolve();
        },
        shape: () => {
          this.store.setEditorElements([element, ...this.store.editorElements]);
          this.store.elementManager.addElement(element);
          resolve();
        },
        image: () => {
          const imageElement = document.createElement("img");
          imageElement.src = element.properties.src;
          imageElement.id = `image-${element.id}`;
          imageElement.style.display = "none";
          document.body.appendChild(imageElement);
          imageElement.onload = () => {
            this.store.setEditorElements([
              element,
              ...this.store.editorElements,
            ]);
            this.store.elementManager.addElement(element);
            resolve();
          };
          imageElement.onerror = () => resolve();
        },
        video: () => {
          const videoElement = document.createElement("video");
          videoElement.style.display = "none";
          videoElement.src = element.properties.src;
          videoElement.id = `video-${element.id}`;
          videoElement.onloadeddata = () => {
            this.store.setEditorElements([
              element,
              ...this.store.editorElements,
            ]);
            this.store.elementManager.addElement(element);
            resolve();
          };
          videoElement.onerror = () => resolve();
          document.body.appendChild(videoElement);
        },
        audio: () => {
          const audio = document.createElement("audio");
          audio.src = element.properties.src;
          audio.id = `audio-${element.id}`;
          audio.onloadeddata = () => {
            this.store.setEditorElements([
              element,
              ...this.store.editorElements,
            ]);
            resolve();
          };
          audio.onerror = () => resolve();
          document.body.appendChild(audio);
        },
      };
      const creator = elementCreators[element.type];
      if (creator) {
        creator();
      } else {
        resolve();
      }
    });
  }

  private _reconstructTracksFromElements() {
    this.store.trackManager.tracks = [];
    const trackIds = new Set<string>();
    this.store.editorElements.forEach((element) => {
      if (element.trackId) {
        trackIds.add(element.trackId);
      }
    });

    trackIds.forEach((trackId) => {
      const elementsWithTrackId = this.store.editorElements.filter(
        (element) => element.trackId === trackId
      );
      if (elementsWithTrackId.length > 0) {
        let trackType: TrackType;
        const firstElement = elementsWithTrackId[0];
        if (firstElement.type === "image" || firstElement.type === "video") {
          trackType = "media";
        } else {
          trackType = firstElement.type as TrackType;
        }
        const track: Track = {
          id: trackId,
          name: `${
            trackType.charAt(0).toUpperCase() + trackType.slice(1)
          } Track`,
          type: trackType,
          elementIds: elementsWithTrackId.map((element) => element.id),
          isVisible: true,
          isLocked: false,
        };
        this.store.trackManager.tracks.push(track);
        if (!this.store.trackManager.defaultTracks[trackType]) {
          this.store.trackManager.defaultTracks[trackType] = trackId;
        }
      }
    });
  }

  private async _importElementsAndMedia(canvasData: any) {
    this.store.setLoading(true, "正在加载元素...");

    const elementsMap = new Map(
      canvasData.elements.map((element: any) => [element.id, element])
    );

    for (const element of [...canvasData.elements].reverse()) {
      const elementWithDefaults = {
        ...element,
        locked: element.locked ?? false,
        opacity: element.opacity ?? 1,
      };
      await this._createElementAsync(elementWithDefaults);
    }

    const updatedElements = this.store.editorElements.map((newElement) => {
      const originalElement: any = elementsMap.get(newElement.id);
      if (newElement && originalElement) {
        return {
          ...newElement,
          placement: originalElement.placement,
          timeFrame: originalElement.timeFrame,
          properties: {
            ...newElement.properties,
            ...originalElement.properties,
          },
          trackId: originalElement.trackId,
          locked: originalElement.locked ?? newElement.locked ?? false,
          opacity: originalElement.opacity ?? newElement.opacity ?? 1,
        };
      }
      return newElement;
    });

    this.store.setEditorElements(updatedElements);

    if (!canvasData.tracks) {
      this._reconstructTracksFromElements();
    } else {
      this.store.editorElements.forEach((element) => {
        if (element.trackId) {
          const track = this.store.trackManager.tracks.find(
            (t) => t.id === element.trackId
          );
          if (track && !track.elementIds.includes(element.id)) {
            track.elementIds.push(element.id);
          }
        }
      });
    }
  }

  private _finalizeImport() {
    this.store.setLoading(true, "正在完成导入...");
    this.store.refreshElements();
    this.store.refreshAnimations();
    this.store.trackManager.cleanupInvalidElementIds();
    this.store.updateCanvasOrderByTrackOrder();
    this.store.updateMaxTime();
    this.store.fitTimelineToContent();
    this.store.historyManager.initHistory();

    setTimeout(() => {
      const allElementsLoaded = this.store.editorElements.every((element) => {
        if (element.type === "text") {
          return (
            element.properties &&
            element.properties.fontSize !== undefined &&
            element.properties.fontColor !== undefined
          );
        }
        return true;
      });

      if (allElementsLoaded) {
        this.store.setLoading(false, "");
        console.log("所有元素加载完成，UI可以安全渲染");
      } else {
        setTimeout(() => {
          this.store.setLoading(false, "");
        }, 300);
      }
    }, 800);
  }

  importCanvasState(jsonState: string) {
    try {
      this._isImporting = true;
      this.store.setLoading(true, "正在加载项目数据...");
      const canvasData = JSON.parse(jsonState);

      this._resetStateForImport();
      this._importCanvasSettings(canvasData);
      this._importCaptions(canvasData);
      this._importTracks(canvasData);

      this._importElementsAndMedia(canvasData).then(() => {
        this._importAnimations(canvasData);
        this._finalizeImport();
        this._isImporting = false;
      });
      return true;
    } catch (error) {
      console.error("Error importing canvas state:", error);
      this._isImporting = false;
      this.store.setLoading(false, "");
      return false;
    }
  }

  saveToLocalStorage() {
    try {
      // 在保存前清理无效的元素ID
      const removedCount = this.store.trackManager.cleanupInvalidElementIds();
      if (removedCount > 0) {
        console.log(`已从轨道中清理 ${removedCount} 个无效的元素ID`);
      }

      // 在保存前删除空轨道
      this.store.trackManager.removeEmptyTracks();

      // 导出状态并保存到本地存储
      const state = this.exportCanvasState();
      localStorage.setItem(this.autoSaveKey, state);
    } catch (error) {
      console.error("Failed to save canvas state to localStorage:", error);
    }
  }

  loadFromLocalStorage() {
    // 设置加载状态为开始加载
    this.store.setLoading(true, "正在从本地存储加载数据...");

    const savedState = localStorage.getItem(this.autoSaveKey);
    if (savedState) {
      console.log("🔄 开始从本地存储加载数据");
      const result = this.importCanvasState(savedState);

      // 确保更新最大时间
      this.store.updateMaxTime();

      console.log("✅ 本地存储数据加载完成，结果:", result);
      return result;
    }

    console.log("ℹ️ 没有找到本地存储的数据");
    // 如果没有保存的状态，也需要重置加载状态
    this.store.setLoading(false, "");
    return false;
  }

  async exportVideo(format: string = "mp4") {
    console.log("exportVideo", format);
    const canvasState = this.exportCanvasState();
    console.log("canvasState", canvasState);
    const response = await fetch("http://localhost:8080/api/generateVideo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: canvasState,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to generate video");
    }

    // 解析响应并返回数据对象
    const responseData = await response.json();
    return responseData;
  }
}
